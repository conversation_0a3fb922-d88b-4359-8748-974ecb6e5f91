plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services' // Added Google Services plugin
}

android {
    compileSdk 35

    defaultConfig {
        applicationId 'com.Zumbla.Burst2025'
        minSdkVersion 23
        targetSdk 35 // Updated from 21 to 23 to resolve compatibility issue
        versionCode 3
        versionName '2.1.0'
        multiDexEnabled true
        manifestPlaceholders = [onesignal_app_id: "************************************"]
    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            debuggable true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled false
            shrinkResources false
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    // إذا لم تعد بحاجة إلى buildToolsVersion، يمكنك حذفه
    // buildToolsVersion '36.0.0'

    // تحديث lintOptions إلى الصيغة الجديدة (AGP 7+)
    lint {
        checkReleaseBuilds false
    }

    // يفضَّل أن يتطابق namespace مع جذر applicationId
    namespace 'com.Zumbla.Burst2025'

    buildFeatures {
        // Removed the old setting for buildConfig
        // Added the new recommended setting
        android.buildFeatures.buildConfig = true
    }
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:33.13.0') // Added Firebase BOM
    implementation 'com.google.firebase:firebase-analytics' // Added Firebase Analytics
    implementation 'androidx.appcompat:appcompat:1.7.0' // الاحتفاظ بالإصدار الأحدث
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'com.google.android.gms:play-services-ads:22.1.0' // Updated AdMob SDK
    implementation 'androidx.core:core:1.16.0' // Updated core library
    implementation 'com.google.android.ump:user-messaging-platform:3.2.0'
    implementation 'androidx.preference:preference:1.2.1' // Updated preference library
    implementation 'com.onesignal:OneSignal:4.8.6'
    implementation 'androidx.activity:activity:1.7.2'
    implementation 'androidx.annotation:annotation:1.6.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.1'

    // Play In-App Update Library
    implementation 'com.google.android.play:app-update:2.1.0'

    // Play In-App Review Library
    implementation 'com.google.android.play:review:2.0.2'

    // Removed the conflicting Play Core library
    // implementation 'com.google.android.play:core:1.10.3'

    // Added Google Play Services Tasks library
    implementation 'com.google.android.gms:play-services-tasks:18.0.1'
}
