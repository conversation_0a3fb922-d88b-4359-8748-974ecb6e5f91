<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:hwads="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <RelativeLayout
        android:id="@+id/relativeLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#ffffff"
        android:gravity="center"
        android:visibility="gone">

        <ImageView
            android:id="@+id/noConnectionLogo"
            android:layout_width="240dp"
            android:layout_height="240dp"
            android:layout_centerHorizontal="true"
            android:contentDescription="@string/image_content"
            android:src="@drawable/no_internet" />

        <TextView
            android:id="@+id/txtNoConnection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/noConnectionLogo"
            android:gravity="center"
            android:text="@string/no_connection"
            android:textAlignment="center"
            android:textSize="26sp" />

        <Button
            android:id="@+id/btnNoConnection"
            android:layout_width="140dp"
            android:layout_height="55dp"
            android:layout_below="@id/txtNoConnection"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="60dp"
            android:background="@color/colorAccent"
            android:clickable="true"
            android:focusable="true"
            android:longClickable="true"
            android:text="@string/txtRetry"
            android:textColor="#ffffff"
            android:textSize="22sp"
            tools:targetApi="m" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/main"
        android:orientation="vertical">

        <com.Zumbla.Burst2025.UtilsAwv
            android:id="@+id/myWebView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:visibility="visible" />

        <com.google.android.gms.ads.AdView
            xmlns:ads="http://schemas.android.com/apk/res-auto"
            android:id="@+id/adView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="0dp"
            ads:adSize="BANNER"
            ads:adUnitId="@string/id_banner">
        </com.google.android.gms.ads.AdView>
    </LinearLayout>

    <VideoView
        android:id="@+id/splash_video"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true"
        android:visibility="gone" />

</RelativeLayout>