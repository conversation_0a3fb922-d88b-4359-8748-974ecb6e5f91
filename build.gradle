// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://plugins.gradle.org/m2/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.0'
        classpath 'com.google.gms:google-services:4.4.2' // Added Google Services plugin
        classpath 'gradle.plugin.com.onesignal:onesignal-gradle-plugin:[0.14.0, 0.99.99]'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

// Removed the OneSignal plugin application from the root project.

allprojects {
    repositories {
        google()
        mavenCentral()
    }
    gradle.projectsEvaluated {
        tasks.withType(JavaCompile){
            options.compilerArgs << "-Xlint:deprecation"
            options.compilerArgs << "-Xlint:unchecked"
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}